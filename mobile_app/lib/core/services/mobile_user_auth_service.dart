import 'package:firebase_auth/firebase_auth.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter/foundation.dart';

/// Mobile User Authentication Service
/// Handles authentication specifically for mobile app users (students/quiz takers)
/// Completely separate from admin authentication
class MobileUserAuthService {
  static final FirebaseAuth _auth = FirebaseAuth.instance;
  static final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  
  // Collection name for mobile users (separate from admin users)
  static const String _mobileUsersCollection = 'mobile_users';

  /// Register a new mobile user with email/password
  static Future<User?> registerMobileUser({
    required String email,
    required String password,
    required String name,
    required String phoneNumber,
    required String officeName,
    required String designation,
  }) async {
    try {
      if (kDebugMode) {
        print('DEBUG: 📱 Registering mobile user: $email');
      }

      // Create Firebase Auth user
      final UserCredential userCredential = await _auth.createUserWithEmailAndPassword(
        email: email,
        password: password,
      );

      final User? user = userCredential.user;
      if (user != null) {
        // Update display name
        await user.updateDisplayName(name);
        
        // Send email verification
        await user.sendEmailVerification();

        // Store mobile user data in Firestore
        await _storeMobileUserData(
          uid: user.uid,
          email: email,
          name: name,
          phoneNumber: phoneNumber,
          officeName: officeName,
          designation: designation,
        );

        if (kDebugMode) {
          print('DEBUG: ✅ Mobile user registered successfully: ${user.uid}');
        }
        
        return user;
      }
    } catch (e) {
      if (kDebugMode) {
        print('DEBUG: ❌ Mobile user registration failed: $e');
      }
      rethrow;
    }
    return null;
  }

  /// Sign in mobile user with email/password
  static Future<User?> signInMobileUser({
    required String email,
    required String password,
  }) async {
    try {
      if (kDebugMode) {
        print('DEBUG: 📱 Signing in mobile user: $email');
      }

      final UserCredential userCredential = await _auth.signInWithEmailAndPassword(
        email: email,
        password: password,
      );

      final User? user = userCredential.user;
      if (user != null) {
        // Update last login time
        await _updateLastLoginTime(user.uid);
        
        if (kDebugMode) {
          print('DEBUG: ✅ Mobile user signed in successfully: ${user.uid}');
        }
        
        return user;
      }
    } catch (e) {
      if (kDebugMode) {
        print('DEBUG: ❌ Mobile user sign in failed: $e');
      }
      rethrow;
    }
    return null;
  }

  /// Send password reset email
  static Future<void> sendPasswordReset(String email) async {
    try {
      await _auth.sendPasswordResetEmail(email: email);
      if (kDebugMode) {
        print('DEBUG: ✅ Password reset email sent to: $email');
      }
    } catch (e) {
      if (kDebugMode) {
        print('DEBUG: ❌ Failed to send password reset email: $e');
      }
      rethrow;
    }
  }

  /// Sign out mobile user
  static Future<void> signOut() async {
    try {
      await _auth.signOut();
      if (kDebugMode) {
        print('DEBUG: ✅ Mobile user signed out');
      }
    } catch (e) {
      if (kDebugMode) {
        print('DEBUG: ❌ Sign out failed: $e');
      }
      rethrow;
    }
  }

  /// Get current mobile user
  static User? getCurrentUser() {
    return _auth.currentUser;
  }

  /// Get mobile user data from Firestore
  static Future<Map<String, dynamic>?> getMobileUserData(String uid) async {
    try {
      final doc = await _firestore.collection(_mobileUsersCollection).doc(uid).get();
      if (doc.exists) {
        return doc.data();
      }
    } catch (e) {
      if (kDebugMode) {
        print('DEBUG: ❌ Failed to get mobile user data: $e');
      }
    }
    return null;
  }

  /// Store mobile user data in Firestore
  static Future<void> _storeMobileUserData({
    required String uid,
    required String email,
    required String name,
    required String phoneNumber,
    required String officeName,
    required String designation,
  }) async {
    try {
      final userData = {
        'uid': uid,
        'email': email,
        'name': name,
        'phoneNumber': phoneNumber,
        'officeName': officeName,
        'designation': designation,
        'userType': 'mobile_user', // Distinguish from admin users
        'emailVerified': false,
        'createdAt': FieldValue.serverTimestamp(),
        'lastLoginAt': FieldValue.serverTimestamp(),
        'profileComplete': true,
        'isActive': true,
        'quizzesTaken': 0,
        'totalScore': 0,
        'averageScore': 0.0,
        'preferences': {
          'notifications': true,
          'darkMode': false,
          'language': 'en',
        },
      };

      await _firestore.collection(_mobileUsersCollection).doc(uid).set(userData);
      
      if (kDebugMode) {
        print('DEBUG: ✅ Mobile user data stored in Firestore');
      }
    } catch (e) {
      if (kDebugMode) {
        print('DEBUG: ❌ Failed to store mobile user data: $e');
      }
      rethrow;
    }
  }

  /// Update last login time
  static Future<void> _updateLastLoginTime(String uid) async {
    try {
      await _firestore.collection(_mobileUsersCollection).doc(uid).update({
        'lastLoginAt': FieldValue.serverTimestamp(),
      });
    } catch (e) {
      if (kDebugMode) {
        print('DEBUG: ❌ Failed to update last login time: $e');
      }
    }
  }

  /// Update mobile user profile
  static Future<void> updateMobileUserProfile({
    required String uid,
    String? name,
    String? phoneNumber,
    String? officeName,
    String? designation,
    Map<String, dynamic>? preferences,
  }) async {
    try {
      final updates = <String, dynamic>{};
      
      if (name != null) updates['name'] = name;
      if (phoneNumber != null) updates['phoneNumber'] = phoneNumber;
      if (officeName != null) updates['officeName'] = officeName;
      if (designation != null) updates['designation'] = designation;
      if (preferences != null) updates['preferences'] = preferences;
      
      updates['updatedAt'] = FieldValue.serverTimestamp();

      await _firestore.collection(_mobileUsersCollection).doc(uid).update(updates);
      
      if (kDebugMode) {
        print('DEBUG: ✅ Mobile user profile updated');
      }
    } catch (e) {
      if (kDebugMode) {
        print('DEBUG: ❌ Failed to update mobile user profile: $e');
      }
      rethrow;
    }
  }

  /// Update quiz statistics
  static Future<void> updateQuizStats({
    required String uid,
    required int score,
    required int totalQuestions,
  }) async {
    try {
      final userDoc = _firestore.collection(_mobileUsersCollection).doc(uid);
      
      await _firestore.runTransaction((transaction) async {
        final snapshot = await transaction.get(userDoc);
        if (snapshot.exists) {
          final data = snapshot.data()!;
          final currentQuizzesTaken = data['quizzesTaken'] ?? 0;
          final currentTotalScore = data['totalScore'] ?? 0;
          
          final newQuizzesTaken = currentQuizzesTaken + 1;
          final newTotalScore = currentTotalScore + score;
          final newAverageScore = newTotalScore / newQuizzesTaken;
          
          transaction.update(userDoc, {
            'quizzesTaken': newQuizzesTaken,
            'totalScore': newTotalScore,
            'averageScore': newAverageScore,
            'lastQuizAt': FieldValue.serverTimestamp(),
          });
        }
      });
      
      if (kDebugMode) {
        print('DEBUG: ✅ Quiz stats updated for user: $uid');
      }
    } catch (e) {
      if (kDebugMode) {
        print('DEBUG: ❌ Failed to update quiz stats: $e');
      }
      rethrow;
    }
  }

  /// Email validation
  static bool isValidEmail(String email) {
    return RegExp(r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$').hasMatch(email);
  }

  /// Password validation
  static String? validatePassword(String password) {
    if (password.length < 6) {
      return 'Password must be at least 6 characters long';
    }
    if (!password.contains(RegExp(r'[A-Z]'))) {
      return 'Password must contain at least one uppercase letter';
    }
    if (!password.contains(RegExp(r'[a-z]'))) {
      return 'Password must contain at least one lowercase letter';
    }
    if (!password.contains(RegExp(r'[0-9]'))) {
      return 'Password must contain at least one number';
    }
    return null;
  }
}
