import 'package:firebase_auth/firebase_auth.dart';
import 'package:flutter/foundation.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'firestore_service.dart';
import 'dart:convert';

/// Enhanced Firebase Auth Service with real-time OTP verification
class FirebaseRealtimeAuthService {
  static final FirebaseAuth _auth = FirebaseAuth.instance;

  // Storage keys for session data
  static const String _registrationDataKey = 'firebase_registration_data';
  static const String _verificationIdKey = 'firebase_verification_id';
  static const String _registrationVerificationIdKey =
      'firebase_registration_verification_id';
  static const String _tempRegistrationDataKey = 'temp_registration_data';
  static const String _resendTokenKey = 'resend_token';

  // In-memory storage as backup for verification ID
  static String? _currentVerificationId;
  static Map<String, dynamic>? _currentRegistrationData;
  static DateTime? _verificationIdTimestamp;

  // Session validity duration (10 minutes)
  static const Duration _sessionValidityDuration = Duration(minutes: 10);

  // Callbacks for real-time updates
  static Function(String)? _onCodeSent;
  static Function(String)? _onVerificationFailed;
  static Function()? _onVerificationCompleted;
  static Function(User)? _onRegistrationSuccess;

  /// Check if the current verification session is still valid
  static bool _isSessionValid() {
    if (_verificationIdTimestamp == null) return false;
    final now = DateTime.now();
    final timeDifference = now.difference(_verificationIdTimestamp!);
    return timeDifference < _sessionValidityDuration;
  }

  /// Clear expired session data
  static Future<void> _clearExpiredSession() async {
    if (!_isSessionValid()) {
      final prefs = await SharedPreferences.getInstance();
      await prefs.remove(_registrationVerificationIdKey);
      await prefs.remove(_tempRegistrationDataKey);
      await prefs.remove(_resendTokenKey);
      _currentVerificationId = null;
      _currentRegistrationData = null;
      _verificationIdTimestamp = null;
      if (kDebugMode) {
        print('DEBUG: 🧹 Cleared expired session data');
      }
    }
  }

  /// Restart registration process with stored data
  static Future<bool> restartRegistrationWithStoredData() async {
    try {
      if (_currentRegistrationData == null) {
        throw Exception('No registration data found to restart with');
      }

      final data = _currentRegistrationData!;
      await sendRegistrationOTP(
        phoneNumber: data['phoneNumber'],
        name: data['name'],
        email: data['email'],
        officeName: data['officeName'],
        designation: data['designation'],
        onCodeSent: _onCodeSent,
        onVerificationFailed: _onVerificationFailed,
        onVerificationCompleted: _onVerificationCompleted,
        onRegistrationSuccess: _onRegistrationSuccess,
      );
      return true;
    } catch (e) {
      if (kDebugMode) {
        print('DEBUG: ❌ Failed to restart registration: $e');
      }
      return false;
    }
  }

  /// Check if phone number format is valid
  static bool isValidPhoneNumber(String phoneNumber) {
    // Remove all non-digit characters except +
    final cleanNumber = phoneNumber.replaceAll(RegExp(r'[^\d+]'), '');

    // Check if it starts with + and has country code
    if (!cleanNumber.startsWith('+')) return false;

    // Check if it has at least 10 digits after country code
    final digitsOnly = cleanNumber.substring(1); // Remove +
    if (digitsOnly.length < 10 || digitsOnly.length > 15) return false;

    // Check if all characters after + are digits
    return RegExp(r'^\d+$').hasMatch(digitsOnly);
  }

  /// Get formatted phone number with proper country code
  static String formatPhoneNumber(String phoneNumber) {
    // Remove all non-digit characters except +
    String cleanNumber = phoneNumber.replaceAll(RegExp(r'[^\d+]'), '');

    // If it doesn't start with +, assume it's an Indian number
    if (!cleanNumber.startsWith('+')) {
      // Remove leading 0 if present
      if (cleanNumber.startsWith('0')) {
        cleanNumber = cleanNumber.substring(1);
      }
      // Add Indian country code
      cleanNumber = '+91$cleanNumber';
    }

    return cleanNumber;
  }

  /// Send OTP for registration with real-time callbacks
  static Future<void> sendRegistrationOTP({
    required String phoneNumber,
    required String name,
    required String email,
    required String officeName,
    required String designation,
    Function(String)? onCodeSent,
    Function(String)? onVerificationFailed,
    Function()? onVerificationCompleted,
    Function(User)? onRegistrationSuccess,
  }) async {
    try {
      // Validate and format phone number
      final formattedPhone = formatPhoneNumber(phoneNumber);
      if (!isValidPhoneNumber(formattedPhone)) {
        throw Exception(
            'Invalid phone number format. Please use format: +************');
      }

      // Store callbacks
      _onCodeSent = onCodeSent;
      _onVerificationFailed = onVerificationFailed;
      _onVerificationCompleted = onVerificationCompleted;
      _onRegistrationSuccess = onRegistrationSuccess;

      // Store temporary registration data
      final prefs = await SharedPreferences.getInstance();
      final registrationData = {
        'name': name,
        'email': email,
        'officeName': officeName,
        'designation': designation,
        'phoneNumber': formattedPhone, // Use formatted phone number
      };
      await prefs.setString(
          _tempRegistrationDataKey, jsonEncode(registrationData));

      // Store in memory as backup
      _currentRegistrationData = registrationData;

      // Check if this is a test phone number (for development/testing)
      if (kDebugMode) {
        print('DEBUG: Checking if $formattedPhone is a test number...');
        print(
            'DEBUG: Test number check result: ${_isTestPhoneNumber(formattedPhone)}');
      }

      if (_isTestPhoneNumber(formattedPhone)) {
        // Handle test phone numbers without Firebase SMS (development mode)
        const testVerificationId = 'test_verification_id';

        // Store in both SharedPreferences and memory
        await prefs.setString(
            _registrationVerificationIdKey, testVerificationId);
        _currentVerificationId = testVerificationId;
        _verificationIdTimestamp =
            DateTime.now(); // Set timestamp for test numbers too

        if (kDebugMode) {
          print('DEBUG: ✅ Using test phone number: $formattedPhone');
          print('DEBUG: ✅ Expected OTP: ${_getTestOTP(formattedPhone)}');
          print('DEBUG: ✅ Verification ID stored: $testVerificationId');
          print('DEBUG: ✅ Storage key used: $_registrationVerificationIdKey');
          print('DEBUG: ✅ In-memory backup stored: $_currentVerificationId');

          // Verify storage immediately
          final storedId = prefs.getString(_registrationVerificationIdKey);
          print('DEBUG: ✅ Verification - stored ID retrieved: $storedId');
        }
        _onCodeSent?.call(
            '🧪 TEST MODE: OTP sent to $formattedPhone\n\nUse OTP: ${_getTestOTP(formattedPhone) ?? "123456"}\n\n✅ This bypasses Firebase completely for testing.');
        return;
      }

      // For real phone numbers - Firebase billing is enabled
      if (kDebugMode) {
        print('DEBUG: 🚀 Processing real phone number with Firebase SMS');
        print('DEBUG: 🚀 Firebase billing is enabled - sending real SMS');
      }

      await _auth.verifyPhoneNumber(
        phoneNumber: formattedPhone,
        verificationCompleted: (PhoneAuthCredential credential) async {
          // Auto-verification completed (Android only)
          try {
            final user = await _completeRegistration(
                credential, name, email, officeName, designation, phoneNumber);
            if (user != null) {
              _onRegistrationSuccess?.call(user);
              _onVerificationCompleted?.call();
            }
          } catch (e) {
            _onVerificationFailed
                ?.call('Auto-verification failed: ${e.toString()}');
          }
        },
        verificationFailed: (FirebaseAuthException e) {
          String errorMessage;
          if (kDebugMode) {
            print('DEBUG: Firebase Auth Error Code: ${e.code}');
            print('DEBUG: Firebase Auth Error Message: ${e.message}');
          }

          if (e.code == 'app-not-authorized' ||
              e.message?.contains('not authorized') == true ||
              e.message?.contains('Invalid app info') == true) {
            errorMessage = '''🔧 Firebase Configuration Issue Detected

This app is not authorized for Firebase Authentication.

🚀 IMMEDIATE SOLUTION - Use Test Numbers:
📱 +************ → OTP: 123456
📱 +************ → OTP: 654321
📱 +************ → OTP: 111111

🔧 TO FIX PERMANENTLY:
1. Get your app's SHA-1 fingerprint
2. Add it to Firebase Console
3. Download new google-services.json
4. Rebuild the app

See FIREBASE_AUTH_FIX.md for detailed instructions.''';
          } else if (e.code == 'billing-not-enabled' ||
              e.message?.contains('billing') == true) {
            // This shouldn't happen since billing is enabled, but keep as fallback
            errorMessage = '''SMS service temporarily unavailable.

🔧 Please try again in a few moments, or use test numbers for development:

📱 Test Numbers (Development):
• +************ → OTP: 123456
• +************ → OTP: 654321
• +************ → OTP: 111111

Note: Firebase billing is enabled but there may be a temporary service issue.''';
          } else if (e.code == 'invalid-phone-number') {
            errorMessage = '''Invalid phone number format.

✅ Correct format: +[country_code][phone_number]
📱 Example: +************

Please check:
• Include country code (+91 for India)
• Remove spaces and special characters
• Use 10-digit phone number after country code''';
          } else if (e.code == 'too-many-requests') {
            errorMessage = '''Too many SMS requests.

⏰ Please wait 1-2 minutes before trying again.

This is a security measure to prevent spam. You can:
• Wait and try again later
• Use test numbers for development: +************ (OTP: 123456)''';
          } else if (e.code == 'quota-exceeded') {
            errorMessage = '''SMS quota exceeded.

📊 Your Firebase project has reached its SMS limit.

Solutions:
• Check Firebase Console → Usage and billing
• Increase SMS quota if needed
• Use test numbers for development''';
          } else if (e.code == 'app-not-authorized') {
            errorMessage = '''App not authorized for SMS.

🔧 Configuration issue detected.

Please check:
• Firebase project configuration
• App registration in Firebase Console
• SHA fingerprints (for Android)''';
          } else if (e.code == 'network-request-failed') {
            errorMessage = '''Network connection failed.

🌐 Please check your internet connection and try again.

• Ensure stable internet connection
• Try switching between WiFi and mobile data
• Check if Firebase services are accessible''';
          } else {
            errorMessage = '''Phone verification failed.

Error: ${e.message ?? 'Unknown error'}
Code: ${e.code}

💡 If this persists:
• Try using a different phone number
• Check your internet connection
• Use test numbers for development: +************ (OTP: 123456)''';
          }
          _onVerificationFailed?.call(errorMessage);
        },
        codeSent: (String verificationId, int? resendToken) async {
          try {
            // Store verification ID and resend token for later use
            await prefs.setString(
                _registrationVerificationIdKey, verificationId);
            _currentVerificationId = verificationId; // In-memory backup
            _verificationIdTimestamp =
                DateTime.now(); // Track when verification ID was created

            if (resendToken != null) {
              await prefs.setInt(_resendTokenKey, resendToken);
            }

            if (kDebugMode) {
              print(
                  'DEBUG: 🚀 Real SMS sent successfully. VerificationId: ${verificationId.substring(0, 10)}...');
              print('DEBUG: 🚀 Firebase billing enabled - real SMS delivered');
              print(
                  'DEBUG: 🚀 Stored verification ID with key: $_registrationVerificationIdKey');

              // Verify storage immediately
              final storedId = prefs.getString(_registrationVerificationIdKey);
              print(
                  'DEBUG: 🚀 Verification - stored ID retrieved: ${storedId?.substring(0, 10)}...');
            }
            _onCodeSent?.call(
                '📱 OTP sent to $formattedPhone\n\nPlease check your SMS messages and enter the 6-digit code.\n\n💡 If you don\'t receive it within 2-3 minutes, try the resend option.');
          } catch (e) {
            if (kDebugMode) {
              print('DEBUG: Error storing verification data: $e');
            }
            _onVerificationFailed
                ?.call('Failed to store verification data: ${e.toString()}');
          }
        },
        codeAutoRetrievalTimeout: (String verificationId) {
          // Auto-retrieval timeout - store the verification ID
          prefs.setString(_registrationVerificationIdKey, verificationId);
        },
        timeout: const Duration(
            seconds: 120), // Increased timeout for better reliability
      );
    } catch (e) {
      final errorMessage = 'Failed to send registration OTP: ${e.toString()}';
      _onVerificationFailed?.call(errorMessage);
      throw Exception(errorMessage);
    }
  }

  /// Verify OTP for registration with real-time feedback
  static Future<User?> verifyRegistrationOTP(String otp) async {
    try {
      final prefs = await SharedPreferences.getInstance();

      // Check if session has expired and clear if necessary
      await _clearExpiredSession();

      var verificationId = prefs.getString(_registrationVerificationIdKey);
      var tempDataString = prefs.getString(_tempRegistrationDataKey);

      // Use in-memory backup if SharedPreferences failed
      if (verificationId == null && _currentVerificationId != null) {
        // Check if in-memory session is still valid
        if (_isSessionValid()) {
          verificationId = _currentVerificationId;
          if (kDebugMode) {
            print(
                'DEBUG: 🔄 Using in-memory verification ID backup: ${verificationId?.substring(0, 10)}...');
          }
        } else {
          if (kDebugMode) {
            print('DEBUG: ❌ In-memory session has expired');
          }
        }
      }

      if (tempDataString == null && _currentRegistrationData != null) {
        tempDataString = jsonEncode(_currentRegistrationData!);
        if (kDebugMode) {
          print('DEBUG: 🔄 Using in-memory registration data backup');
        }
      }

      if (kDebugMode) {
        print('DEBUG: 🔍 Checking stored data...');
        print('DEBUG: 🔍 VerificationId from storage: $verificationId');
        print('DEBUG: 🔍 VerificationId from memory: $_currentVerificationId');
        print(
            'DEBUG: 🔍 TempData from storage: ${tempDataString != null ? 'Found' : 'Not found'}');

        // List all stored keys for debugging
        final allKeys = prefs.getKeys();
        print('DEBUG: 🔍 All stored keys: $allKeys');
      }

      if (verificationId == null) {
        if (kDebugMode) {
          print('DEBUG: ❌ Verification ID is null - checking all sources');
          print(
              'DEBUG: ❌ SharedPreferences verification ID: ${prefs.getString(_registrationVerificationIdKey)}');
          print('DEBUG: ❌ In-memory verification ID: $_currentVerificationId');
          final allKeys = prefs.getKeys();
          for (String key in allKeys) {
            if (key.contains('verification')) {
              print(
                  'DEBUG: 🔍 Found verification key: $key = ${prefs.getString(key)}');
            }
          }
        }
        throw Exception('Verification ID not found. Please try again.');
      }

      if (tempDataString == null) {
        if (kDebugMode) {
          print('DEBUG: ❌ Registration data is null');
        }
        throw Exception('Registration data not found. Please try again.');
      }

      // Parse registration data
      final registrationData =
          jsonDecode(tempDataString) as Map<String, dynamic>;
      final name = registrationData['name'] as String;
      final email = registrationData['email'] as String;
      final officeName = registrationData['officeName'] as String;
      final designation = registrationData['designation'] as String;
      final phoneNumber = registrationData['phoneNumber'] as String;

      // Handle test phone numbers
      if (kDebugMode) {
        print('DEBUG: OTP Verification - verificationId: $verificationId');
        print('DEBUG: OTP Verification - phoneNumber: $phoneNumber');
        print(
            'DEBUG: OTP Verification - isTestNumber: ${_isTestPhoneNumber(phoneNumber)}');
        print('DEBUG: OTP Verification - entered OTP: $otp');
      }

      if (verificationId == 'test_verification_id' &&
          _isTestPhoneNumber(phoneNumber)) {
        if (kDebugMode) {
          print('DEBUG: ✅ Processing test phone number verification');
          print('DEBUG: ✅ Expected OTP: ${_getTestOTP(phoneNumber)}');
        }

        if (!_verifyTestOTP(phoneNumber, otp)) {
          if (kDebugMode) {
            print('DEBUG: ❌ Invalid test OTP entered');
          }
          throw Exception(
              'Invalid test OTP. Expected: ${_getTestOTP(phoneNumber)}');
        }

        if (kDebugMode) {
          print('DEBUG: ✅ Test OTP verified successfully');
        }

        // For test numbers, create user directly without Firebase Auth
        final user = await _completeTestRegistration(
            name, email, officeName, designation, phoneNumber);

        if (user != null) {
          // Clear temporary data
          await prefs.remove(_registrationVerificationIdKey);
          await prefs.remove(_tempRegistrationDataKey);
          await prefs.remove(_resendTokenKey);

          // Clear in-memory backup
          _currentVerificationId = null;
          _currentRegistrationData = null;

          _onRegistrationSuccess?.call(user);
        }

        return user;
      }

      // Handle real phone numbers with Firebase Auth
      if (kDebugMode) {
        print('DEBUG: 🚀 Processing real phone number verification');
        print('DEBUG: 🚀 Using Firebase Auth with real SMS OTP');
        print(
            'DEBUG: 🚀 VerificationId: ${verificationId.substring(0, 10)}...');
        print('DEBUG: 🚀 OTP length: ${otp.length}');
      }

      // Validate OTP format before creating credential
      if (otp.length != 6 || !RegExp(r'^\d{6}$').hasMatch(otp)) {
        throw Exception('Invalid OTP format. Please enter a 6-digit code.');
      }

      // Validate verification ID format
      if (verificationId.isEmpty || verificationId == 'test_verification_id') {
        throw Exception(
            'Invalid verification session. Please restart registration.');
      }

      // Create credential and sign in for real phone numbers
      final credential = PhoneAuthProvider.credential(
        verificationId: verificationId,
        smsCode: otp,
      );

      if (kDebugMode) {
        print('DEBUG: 🚀 Firebase credential created successfully');
        print('DEBUG: 🚀 Attempting Firebase authentication...');
      }

      final user = await _completeRegistration(
          credential, name, email, officeName, designation, phoneNumber);

      if (user != null) {
        // Clear temporary data
        await prefs.remove(_registrationVerificationIdKey);
        await prefs.remove(_tempRegistrationDataKey);
        await prefs.remove(_resendTokenKey);

        // Clear in-memory backup
        _currentVerificationId = null;
        _currentRegistrationData = null;

        _onRegistrationSuccess?.call(user);
      }

      return user;
    } catch (e) {
      if (kDebugMode) {
        print('DEBUG: ❌ Registration OTP verification failed: $e');
        print('DEBUG: 🔍 Error type: ${e.runtimeType}');
      }

      if (e is FirebaseAuthException) {
        String errorMessage;
        switch (e.code) {
          case 'invalid-verification-code':
            errorMessage = 'Invalid OTP. Please check the code and try again.';
            break;
          case 'invalid-verification-id':
            errorMessage =
                'Verification session expired. Please restart registration.';
            break;
          case 'session-expired':
            errorMessage = 'OTP has expired. Please request a new one.';
            break;
          case 'too-many-requests':
            errorMessage = 'Too many attempts. Please try again later.';
            break;
          case 'app-not-authorized':
            errorMessage =
                'App not authorized for SMS. Please check Firebase configuration.';
            break;
          case 'billing-not-enabled':
            errorMessage =
                'Firebase SMS billing not enabled. Please enable billing in Firebase Console.';
            break;
          default:
            errorMessage =
                'Authentication failed: ${e.message ?? 'Unknown error'}';
        }
        if (kDebugMode) {
          print('DEBUG: 🔍 Firebase error code: ${e.code}');
          print('DEBUG: 🔍 Firebase error message: ${e.message}');
        }
        throw Exception(errorMessage);
      } else if (e.toString().contains('Verification ID not found')) {
        throw Exception(
            'Verification session expired. Please restart registration.');
      } else if (e.toString().contains('Invalid OTP format')) {
        throw Exception(e.toString());
      } else if (e.toString().contains('Invalid verification session')) {
        throw Exception(e.toString());
      }
      throw Exception('Failed to verify registration OTP: ${e.toString()}');
    }
  }

  /// Resend OTP for registration
  static Future<void> resendRegistrationOTP() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final tempDataString = prefs.getString(_tempRegistrationDataKey);
      final resendToken = prefs.getInt(_resendTokenKey);

      if (tempDataString == null) {
        throw Exception(
            'Registration data not found. Please start registration again.');
      }

      // Parse registration data
      final registrationData =
          jsonDecode(tempDataString) as Map<String, dynamic>;
      final name = registrationData['name'] as String;
      final email = registrationData['email'] as String;
      final officeName = registrationData['officeName'] as String;
      final designation = registrationData['designation'] as String;
      final phoneNumber = registrationData['phoneNumber'] as String;

      await _auth.verifyPhoneNumber(
        phoneNumber: phoneNumber,
        forceResendingToken: resendToken,
        verificationCompleted: (PhoneAuthCredential credential) async {
          try {
            final user = await _completeRegistration(
                credential, name, email, officeName, designation, phoneNumber);
            if (user != null) {
              _onRegistrationSuccess?.call(user);
              _onVerificationCompleted?.call();
            }
          } catch (e) {
            _onVerificationFailed
                ?.call('Auto-verification failed: ${e.toString()}');
          }
        },
        verificationFailed: (FirebaseAuthException e) {
          String errorMessage;
          if (e.code == 'too-many-requests') {
            errorMessage =
                'Too many requests. Please wait before requesting another OTP.';
          } else {
            errorMessage =
                'Failed to resend OTP: ${e.message ?? 'Unknown error'}';
          }
          _onVerificationFailed?.call(errorMessage);
        },
        codeSent: (String verificationId, int? newResendToken) async {
          await prefs.setString(_registrationVerificationIdKey, verificationId);
          if (newResendToken != null) {
            await prefs.setInt(_resendTokenKey, newResendToken);
          }
          _onCodeSent?.call('OTP resent successfully');
        },
        codeAutoRetrievalTimeout: (String verificationId) {
          prefs.setString(_registrationVerificationIdKey, verificationId);
        },
        timeout: const Duration(
            seconds: 120), // Increased timeout for better reliability
      );
    } catch (e) {
      final errorMessage = 'Failed to resend OTP: ${e.toString()}';
      _onVerificationFailed?.call(errorMessage);
      throw Exception(errorMessage);
    }
  }

  /// Complete test registration process (for test phone numbers)
  static Future<User?> _completeTestRegistration(
    String name,
    String email,
    String officeName,
    String designation,
    String phoneNumber,
  ) async {
    try {
      if (kDebugMode) {
        print('DEBUG: Starting test registration completion...');
        print(
            'DEBUG: Name: $name, Email: $email, Office: $officeName, Designation: $designation');
        print('DEBUG: Test phone: $phoneNumber');
      }

      // For test numbers, create a mock user ID
      final testUid =
          'test_${phoneNumber.replaceAll('+', '').replaceAll(' ', '')}';

      if (kDebugMode) {
        print('DEBUG: Using test UID: $testUid');
      }

      // Create Firestore document for test user
      if (kDebugMode) print('DEBUG: Creating test user Firestore document...');
      await FirestoreService.createUserDocument(
        uid: testUid,
        name: name,
        email: email,
        phoneNumber: phoneNumber,
        officeName: officeName,
        designation: designation,
      );

      if (kDebugMode) {
        print('DEBUG: Test user document created successfully');
      }

      // Return a mock user object (we can't return a real Firebase User for test numbers)
      // The app will need to handle this case appropriately
      return null; // This will be handled by the calling code
    } catch (e) {
      if (kDebugMode) {
        print('DEBUG: Error in test registration completion: $e');
      }
      throw Exception('Failed to complete test registration: ${e.toString()}');
    }
  }

  /// Complete registration process
  static Future<User?> _completeRegistration(
    PhoneAuthCredential credential,
    String name,
    String email,
    String officeName,
    String designation,
    String phoneNumber,
  ) async {
    try {
      if (kDebugMode) {
        print('DEBUG: Starting registration completion...');
        print(
            'DEBUG: Name: $name, Email: $email, Office: $officeName, Designation: $designation');
      }

      // Sign in with credential
      final userCredential = await _auth.signInWithCredential(credential);
      final user = userCredential.user;

      if (user != null) {
        if (kDebugMode) {
          print('DEBUG: User signed in successfully. UID: ${user.uid}');
          print('DEBUG: User phone: ${user.phoneNumber}');
        }

        // Update user profile
        await user.updateDisplayName(name);
        if (kDebugMode) print('DEBUG: User display name updated');

        // Create Firestore document
        if (kDebugMode) print('DEBUG: Creating Firestore document...');
        await FirestoreService.createUserDocument(
          uid: user.uid,
          name: name,
          email: email,
          phoneNumber: user.phoneNumber ?? phoneNumber,
          officeName: officeName,
          designation: designation,
        );
        if (kDebugMode) print('DEBUG: Firestore document created successfully');

        // Store registration data locally
        final prefs = await SharedPreferences.getInstance();
        final registrationData = {
          'name': name,
          'email': email,
          'phoneNumber': user.phoneNumber ?? phoneNumber,
          'officeName': officeName,
          'designation': designation,
          'registrationDate': DateTime.now().toIso8601String(),
        };
        await prefs.setString(
            _registrationDataKey, jsonEncode(registrationData));
        if (kDebugMode) print('DEBUG: Registration data stored locally');

        // Sign out the user after registration (they need to login separately)
        await _auth.signOut();
        if (kDebugMode) print('DEBUG: User signed out after registration');
      } else {
        if (kDebugMode) print('DEBUG: ERROR - User is null after sign in');
      }

      return user;
    } catch (e) {
      throw Exception('Failed to complete registration: ${e.toString()}');
    }
  }

  /// Send OTP for login with real-time callbacks
  static Future<void> sendLoginOTP({
    required String phoneNumber,
    Function(String)? onCodeSent,
    Function(String)? onVerificationFailed,
    Function()? onVerificationCompleted,
  }) async {
    try {
      final prefs = await SharedPreferences.getInstance();

      await _auth.verifyPhoneNumber(
        phoneNumber: phoneNumber,
        verificationCompleted: (PhoneAuthCredential credential) async {
          try {
            await _auth.signInWithCredential(credential);
            onVerificationCompleted?.call();
          } catch (e) {
            onVerificationFailed
                ?.call('Auto-verification failed: ${e.toString()}');
          }
        },
        verificationFailed: (FirebaseAuthException e) {
          String errorMessage;
          if (e.code == 'billing-not-enabled') {
            errorMessage = 'Firebase SMS billing is not enabled.';
          } else if (e.code == 'invalid-phone-number') {
            errorMessage = 'Invalid phone number format.';
          } else if (e.code == 'too-many-requests') {
            errorMessage = 'Too many requests. Please try again later.';
          } else {
            errorMessage =
                'Phone verification failed: ${e.message ?? 'Unknown error'}';
          }
          onVerificationFailed?.call(errorMessage);
        },
        codeSent: (String verificationId, int? resendToken) async {
          await prefs.setString(_verificationIdKey, verificationId);
          onCodeSent?.call('OTP sent successfully');
        },
        codeAutoRetrievalTimeout: (String verificationId) {
          prefs.setString(_verificationIdKey, verificationId);
        },
        timeout: const Duration(
            seconds: 120), // Increased timeout for better reliability
      );
    } catch (e) {
      final errorMessage = 'Failed to send login OTP: ${e.toString()}';
      onVerificationFailed?.call(errorMessage);
      throw Exception(errorMessage);
    }
  }

  /// Verify OTP for login
  static Future<User?> verifyLoginOTP(String otp) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final verificationId = prefs.getString(_verificationIdKey);

      if (kDebugMode) {
        print('DEBUG: 🔍 Login OTP Verification');
        print(
            'DEBUG: 🔍 VerificationId: ${verificationId != null ? '${verificationId.substring(0, 10)}...' : 'null'}');
        print('DEBUG: 🔍 OTP length: ${otp.length}');
      }

      if (verificationId == null) {
        if (kDebugMode) {
          print('DEBUG: ❌ Verification ID not found in SharedPreferences');
          final allKeys = prefs.getKeys();
          print('DEBUG: 🔍 Available keys: $allKeys');
        }
        throw Exception(
            'Verification ID not found. Please restart login process.');
      }

      // Validate OTP format
      if (otp.length != 6 || !RegExp(r'^\d{6}$').hasMatch(otp)) {
        throw Exception('Invalid OTP format. Please enter a 6-digit code.');
      }

      // Validate verification ID format
      if (verificationId.isEmpty) {
        throw Exception('Invalid verification session. Please restart login.');
      }

      // Create credential and sign in
      final credential = PhoneAuthProvider.credential(
        verificationId: verificationId,
        smsCode: otp,
      );

      if (kDebugMode) {
        print('DEBUG: 🚀 Attempting Firebase login authentication...');
      }

      final userCredential = await _auth.signInWithCredential(credential);
      final user = userCredential.user;

      if (user != null) {
        if (kDebugMode) {
          print('DEBUG: ✅ Login successful for user: ${user.uid}');
        }

        // Update last login in Firestore
        await FirestoreService.updateLastLogin(user.uid);

        // Clear verification ID
        await prefs.remove(_verificationIdKey);
      }

      return user;
    } catch (e) {
      if (kDebugMode) {
        print('DEBUG: ❌ Login OTP verification failed: $e');
        print('DEBUG: 🔍 Error type: ${e.runtimeType}');
      }

      if (e is FirebaseAuthException) {
        String errorMessage;
        switch (e.code) {
          case 'invalid-verification-code':
            errorMessage = 'Invalid OTP. Please check the code and try again.';
            break;
          case 'invalid-verification-id':
            errorMessage =
                'Verification session expired. Please restart login.';
            break;
          case 'session-expired':
            errorMessage = 'OTP has expired. Please request a new one.';
            break;
          case 'too-many-requests':
            errorMessage = 'Too many attempts. Please try again later.';
            break;
          case 'app-not-authorized':
            errorMessage =
                'App not authorized for SMS. Please check Firebase configuration.';
            break;
          case 'billing-not-enabled':
            errorMessage =
                'Firebase SMS billing not enabled. Please enable billing in Firebase Console.';
            break;
          default:
            errorMessage =
                'Authentication failed: ${e.message ?? 'Unknown error'}';
        }
        if (kDebugMode) {
          print('DEBUG: 🔍 Firebase error code: ${e.code}');
          print('DEBUG: 🔍 Firebase error message: ${e.message}');
        }
        throw Exception(errorMessage);
      } else if (e.toString().contains('Verification ID not found')) {
        throw Exception('Verification session expired. Please restart login.');
      } else if (e.toString().contains('Invalid OTP format')) {
        throw Exception(e.toString());
      } else if (e.toString().contains('Invalid verification session')) {
        throw Exception(e.toString());
      }
      throw Exception('Failed to verify login OTP: ${e.toString()}');
    }
  }

  /// Clear all stored data
  static Future<void> clearStoredData() async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.remove(_verificationIdKey);
    await prefs.remove(_registrationVerificationIdKey);
    await prefs.remove(_tempRegistrationDataKey);
    await prefs.remove(_resendTokenKey);
  }

  /// Get current user
  static User? getCurrentUser() {
    return _auth.currentUser;
  }

  /// Sign out
  static Future<void> signOut() async {
    await _auth.signOut();
    await clearStoredData();
  }

  /// Check if phone number is a test number
  static bool _isTestPhoneNumber(String phoneNumber) {
    // Normalize phone number by removing spaces and other formatting
    final normalizedPhone = phoneNumber.replaceAll(RegExp(r'[^\d+]'), '');

    const testNumbers = [
      '+************',
      '+************',
      '+************',
    ];

    // In debug mode, also treat any number ending with specific patterns as test numbers
    if (kDebugMode) {
      // Any number ending with 43210, 67890, or 99999 is treated as test number
      if (normalizedPhone.endsWith('43210') ||
          normalizedPhone.endsWith('67890') ||
          normalizedPhone.endsWith('99999')) {
        return true;
      }
    }

    return testNumbers.contains(normalizedPhone);
  }

  /// Get test OTP for test phone numbers
  static String? _getTestOTP(String phoneNumber) {
    // Normalize phone number by removing spaces and other formatting
    final normalizedPhone = phoneNumber.replaceAll(RegExp(r'[^\d+]'), '');

    const testOTPs = {
      '+************': '123456',
      '+************': '654321',
      '+************': '111111',
    };

    // Check exact matches first
    if (testOTPs.containsKey(normalizedPhone)) {
      return testOTPs[normalizedPhone];
    }

    // In debug mode, provide default OTP for pattern-based test numbers
    if (kDebugMode) {
      if (normalizedPhone.endsWith('43210')) return '123456';
      if (normalizedPhone.endsWith('67890')) return '654321';
      if (normalizedPhone.endsWith('99999')) return '111111';
    }

    return null;
  }

  /// Verify test OTP for test phone numbers
  static bool _verifyTestOTP(String phoneNumber, String otp) {
    final expectedOTP = _getTestOTP(phoneNumber);
    return expectedOTP != null && expectedOTP == otp;
  }
}
