import 'package:flutter/foundation.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:firebase_auth/firebase_auth.dart';
import '../services/mobile_user_auth_service.dart';

/// Mobile User Model (separate from admin users)
class MobileUser {
  final String uid;
  final String email;
  final String name;
  final String phoneNumber;
  final String officeName;
  final String designation;
  final bool emailVerified;
  final bool isActive;
  final int quizzesTaken;
  final int totalScore;
  final double averageScore;
  final DateTime? createdAt;
  final DateTime? lastLoginAt;
  final Map<String, dynamic>? preferences;

  const MobileUser({
    required this.uid,
    required this.email,
    required this.name,
    required this.phoneNumber,
    required this.officeName,
    required this.designation,
    required this.emailVerified,
    required this.isActive,
    this.quizzesTaken = 0,
    this.totalScore = 0,
    this.averageScore = 0.0,
    this.createdAt,
    this.lastLoginAt,
    this.preferences,
  });

  factory MobileUser.fromFirestore(Map<String, dynamic> data) {
    return MobileUser(
      uid: data['uid'] ?? '',
      email: data['email'] ?? '',
      name: data['name'] ?? '',
      phoneNumber: data['phoneNumber'] ?? '',
      officeName: data['officeName'] ?? '',
      designation: data['designation'] ?? '',
      emailVerified: data['emailVerified'] ?? false,
      isActive: data['isActive'] ?? true,
      quizzesTaken: data['quizzesTaken'] ?? 0,
      totalScore: data['totalScore'] ?? 0,
      averageScore: (data['averageScore'] ?? 0.0).toDouble(),
      createdAt: data['createdAt']?.toDate(),
      lastLoginAt: data['lastLoginAt']?.toDate(),
      preferences: data['preferences'],
    );
  }

  MobileUser copyWith({
    String? uid,
    String? email,
    String? name,
    String? phoneNumber,
    String? officeName,
    String? designation,
    bool? emailVerified,
    bool? isActive,
    int? quizzesTaken,
    int? totalScore,
    double? averageScore,
    DateTime? createdAt,
    DateTime? lastLoginAt,
    Map<String, dynamic>? preferences,
  }) {
    return MobileUser(
      uid: uid ?? this.uid,
      email: email ?? this.email,
      name: name ?? this.name,
      phoneNumber: phoneNumber ?? this.phoneNumber,
      officeName: officeName ?? this.officeName,
      designation: designation ?? this.designation,
      emailVerified: emailVerified ?? this.emailVerified,
      isActive: isActive ?? this.isActive,
      quizzesTaken: quizzesTaken ?? this.quizzesTaken,
      totalScore: totalScore ?? this.totalScore,
      averageScore: averageScore ?? this.averageScore,
      createdAt: createdAt ?? this.createdAt,
      lastLoginAt: lastLoginAt ?? this.lastLoginAt,
      preferences: preferences ?? this.preferences,
    );
  }
}

/// Mobile User Authentication State
class MobileUserAuthState {
  final MobileUser? user;
  final bool isLoading;
  final bool isRegistering;
  final bool isLoggingIn;
  final String? error;
  final bool isAuthenticated;

  const MobileUserAuthState({
    this.user,
    this.isLoading = false,
    this.isRegistering = false,
    this.isLoggingIn = false,
    this.error,
    this.isAuthenticated = false,
  });

  MobileUserAuthState copyWith({
    MobileUser? user,
    bool? isLoading,
    bool? isRegistering,
    bool? isLoggingIn,
    String? error,
    bool? isAuthenticated,
  }) {
    return MobileUserAuthState(
      user: user ?? this.user,
      isLoading: isLoading ?? this.isLoading,
      isRegistering: isRegistering ?? this.isRegistering,
      isLoggingIn: isLoggingIn ?? this.isLoggingIn,
      error: error,
      isAuthenticated: isAuthenticated ?? this.isAuthenticated,
    );
  }
}

/// Mobile User Authentication Provider
class MobileUserAuthNotifier extends StateNotifier<MobileUserAuthState> {
  MobileUserAuthNotifier() : super(const MobileUserAuthState()) {
    _checkAuthState();
    _listenToAuthChanges();
  }

  /// Check current authentication state
  void _checkAuthState() async {
    final user = MobileUserAuthService.getCurrentUser();
    if (user != null) {
      await _loadUserData(user.uid);
    }
  }

  /// Listen to Firebase Auth state changes
  void _listenToAuthChanges() {
    FirebaseAuth.instance.authStateChanges().listen((User? user) async {
      if (user != null) {
        await _loadUserData(user.uid);
      } else {
        state = const MobileUserAuthState();
      }
    });
  }

  /// Load user data from Firestore
  Future<void> _loadUserData(String uid) async {
    try {
      final userData = await MobileUserAuthService.getMobileUserData(uid);
      if (userData != null) {
        final mobileUser = MobileUser.fromFirestore(userData);
        state = state.copyWith(
          user: mobileUser,
          isAuthenticated: true,
          error: null,
        );
      }
    } catch (e) {
      if (kDebugMode) {
        print('DEBUG: ❌ Failed to load user data: $e');
      }
      state = state.copyWith(error: e.toString());
    }
  }

  /// Register new mobile user
  Future<bool> registerUser({
    required String email,
    required String password,
    required String name,
    required String phoneNumber,
    required String officeName,
    required String designation,
  }) async {
    state = state.copyWith(isRegistering: true, error: null);

    try {
      final user = await MobileUserAuthService.registerMobileUser(
        email: email,
        password: password,
        name: name,
        phoneNumber: phoneNumber,
        officeName: officeName,
        designation: designation,
      );

      if (user != null) {
        await _loadUserData(user.uid);
        state = state.copyWith(isRegistering: false);
        return true;
      }
    } catch (e) {
      state = state.copyWith(
        isRegistering: false,
        error: _getErrorMessage(e),
      );
    }
    return false;
  }

  /// Sign in mobile user
  Future<bool> signInUser({
    required String email,
    required String password,
  }) async {
    state = state.copyWith(isLoggingIn: true, error: null);

    try {
      final user = await MobileUserAuthService.signInMobileUser(
        email: email,
        password: password,
      );

      if (user != null) {
        await _loadUserData(user.uid);
        state = state.copyWith(isLoggingIn: false);
        return true;
      }
    } catch (e) {
      state = state.copyWith(
        isLoggingIn: false,
        error: _getErrorMessage(e),
      );
    }
    return false;
  }

  /// Send password reset email
  Future<bool> sendPasswordReset(String email) async {
    state = state.copyWith(isLoading: true, error: null);

    try {
      await MobileUserAuthService.sendPasswordReset(email);
      state = state.copyWith(isLoading: false);
      return true;
    } catch (e) {
      state = state.copyWith(
        isLoading: false,
        error: _getErrorMessage(e),
      );
      return false;
    }
  }

  /// Sign out user
  Future<void> signOut() async {
    try {
      await MobileUserAuthService.signOut();
      state = const MobileUserAuthState();
    } catch (e) {
      state = state.copyWith(error: _getErrorMessage(e));
    }
  }

  /// Update user profile
  Future<bool> updateProfile({
    String? name,
    String? phoneNumber,
    String? officeName,
    String? designation,
    Map<String, dynamic>? preferences,
  }) async {
    if (state.user == null) return false;

    state = state.copyWith(isLoading: true, error: null);

    try {
      await MobileUserAuthService.updateMobileUserProfile(
        uid: state.user!.uid,
        name: name,
        phoneNumber: phoneNumber,
        officeName: officeName,
        designation: designation,
        preferences: preferences,
      );

      await _loadUserData(state.user!.uid);
      state = state.copyWith(isLoading: false);
      return true;
    } catch (e) {
      state = state.copyWith(
        isLoading: false,
        error: _getErrorMessage(e),
      );
      return false;
    }
  }

  /// Update quiz statistics
  Future<void> updateQuizStats({
    required int score,
    required int totalQuestions,
  }) async {
    if (state.user == null) return;

    try {
      await MobileUserAuthService.updateQuizStats(
        uid: state.user!.uid,
        score: score,
        totalQuestions: totalQuestions,
      );
      await _loadUserData(state.user!.uid);
    } catch (e) {
      if (kDebugMode) {
        print('DEBUG: ❌ Failed to update quiz stats: $e');
      }
    }
  }

  /// Clear error state
  void clearError() {
    state = state.copyWith(error: null);
  }

  /// Get user-friendly error message
  String _getErrorMessage(dynamic error) {
    if (error is FirebaseAuthException) {
      switch (error.code) {
        case 'user-not-found':
          return 'No account found with this email address.';
        case 'wrong-password':
          return 'Incorrect password. Please try again.';
        case 'email-already-in-use':
          return 'An account already exists with this email address.';
        case 'weak-password':
          return 'Password is too weak. Please choose a stronger password.';
        case 'invalid-email':
          return 'Please enter a valid email address.';
        case 'user-disabled':
          return 'This account has been disabled.';
        case 'too-many-requests':
          return 'Too many failed attempts. Please try again later.';
        default:
          return error.message ?? 'An authentication error occurred.';
      }
    }
    return error.toString();
  }
}

/// Mobile User Authentication Provider
final mobileUserAuthProvider = StateNotifierProvider<MobileUserAuthNotifier, MobileUserAuthState>((ref) {
  return MobileUserAuthNotifier();
});

/// Current Mobile User Provider
final currentMobileUserProvider = Provider<MobileUser?>((ref) {
  final authState = ref.watch(mobileUserAuthProvider);
  return authState.user;
});
