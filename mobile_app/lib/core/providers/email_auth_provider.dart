import 'package:flutter/foundation.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:firebase_auth/firebase_auth.dart';
import '../services/email_auth_service.dart';

/// User model for email authentication
class EmailUser {
  final String uid;
  final String email;
  final String name;
  final String phoneNumber;
  final String officeName;
  final String designation;
  final bool emailVerified;
  final DateTime? createdAt;
  final DateTime? lastLoginAt;

  const EmailUser({
    required this.uid,
    required this.email,
    required this.name,
    required this.phoneNumber,
    required this.officeName,
    required this.designation,
    required this.emailVerified,
    this.createdAt,
    this.lastLoginAt,
  });

  EmailUser copyWith({
    String? uid,
    String? email,
    String? name,
    String? phoneNumber,
    String? officeName,
    String? designation,
    bool? emailVerified,
    DateTime? createdAt,
    DateTime? lastLoginAt,
  }) {
    return EmailUser(
      uid: uid ?? this.uid,
      email: email ?? this.email,
      name: name ?? this.name,
      phoneNumber: phoneNumber ?? this.phoneNumber,
      officeName: officeName ?? this.officeName,
      designation: designation ?? this.designation,
      emailVerified: emailVerified ?? this.emailVerified,
      createdAt: createdAt ?? this.createdAt,
      lastLoginAt: lastLoginAt ?? this.lastLoginAt,
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'uid': uid,
      'email': email,
      'name': name,
      'phoneNumber': phoneNumber,
      'officeName': officeName,
      'designation': designation,
      'emailVerified': emailVerified,
      'createdAt': createdAt?.toIso8601String(),
      'lastLoginAt': lastLoginAt?.toIso8601String(),
    };
  }

  factory EmailUser.fromFirebaseUser(User user, Map<String, dynamic> userData) {
    return EmailUser(
      uid: user.uid,
      email: user.email ?? userData['email'] ?? '',
      name: userData['name'] ?? user.displayName ?? '',
      phoneNumber: userData['phoneNumber'] ?? '',
      officeName: userData['officeName'] ?? '',
      designation: userData['designation'] ?? '',
      emailVerified: user.emailVerified,
      createdAt: userData['createdAt'] != null 
          ? DateTime.parse(userData['createdAt']) 
          : null,
      lastLoginAt: userData['lastLoginAt'] != null 
          ? DateTime.parse(userData['lastLoginAt']) 
          : null,
    );
  }
}

/// Email Authentication state
class EmailAuthState {
  final EmailUser? user;
  final bool isLoading;
  final String? error;
  final bool isAuthenticated;
  final bool isRegistering;
  final bool isLoggingIn;

  const EmailAuthState({
    this.user,
    this.isLoading = false,
    this.error,
    this.isAuthenticated = false,
    this.isRegistering = false,
    this.isLoggingIn = false,
  });

  EmailAuthState copyWith({
    EmailUser? user,
    bool? isLoading,
    String? error,
    bool? isAuthenticated,
    bool? isRegistering,
    bool? isLoggingIn,
  }) {
    return EmailAuthState(
      user: user ?? this.user,
      isLoading: isLoading ?? this.isLoading,
      error: error,
      isAuthenticated: isAuthenticated ?? this.isAuthenticated,
      isRegistering: isRegistering ?? this.isRegistering,
      isLoggingIn: isLoggingIn ?? this.isLoggingIn,
    );
  }
}

/// Email Authentication Provider
class EmailAuthNotifier extends StateNotifier<EmailAuthState> {
  EmailAuthNotifier() : super(const EmailAuthState()) {
    _checkAuthState();
  }

  /// Check current authentication state
  void _checkAuthState() {
    final user = EmailAuthService.getCurrentUser();
    if (user != null) {
      _loadUserData(user);
    }
  }

  /// Load user data from Firestore
  Future<void> _loadUserData(User firebaseUser) async {
    try {
      final userData = await EmailAuthService.getUserData(firebaseUser.uid);
      if (userData != null) {
        final emailUser = EmailUser.fromFirebaseUser(firebaseUser, userData);
        state = state.copyWith(
          user: emailUser,
          isAuthenticated: true,
          error: null,
        );
      }
    } catch (e) {
      if (kDebugMode) {
        print('DEBUG: ❌ Failed to load user data: $e');
      }
    }
  }

  /// Register new user with email and password
  Future<bool> registerUser({
    required String email,
    required String password,
    required String name,
    required String phoneNumber,
    required String officeName,
    required String designation,
  }) async {
    state = state.copyWith(isRegistering: true, isLoading: true, error: null);

    try {
      if (kDebugMode) {
        print('DEBUG: 📧 Starting registration for: $email');
      }

      final user = await EmailAuthService.registerWithEmailPassword(
        email: email,
        password: password,
        name: name,
        phoneNumber: phoneNumber,
        officeName: officeName,
        designation: designation,
      );

      if (user != null) {
        // Load user data from Firestore
        final userData = await EmailAuthService.getUserData(user.uid);
        if (userData != null) {
          final emailUser = EmailUser.fromFirebaseUser(user, userData);
          state = state.copyWith(
            user: emailUser,
            isAuthenticated: true,
            isRegistering: false,
            isLoading: false,
            error: null,
          );

          if (kDebugMode) {
            print('DEBUG: ✅ Registration successful: ${user.uid}');
          }
          return true;
        }
      }
    } catch (e) {
      if (kDebugMode) {
        print('DEBUG: ❌ Registration failed: $e');
      }
      state = state.copyWith(
        isRegistering: false,
        isLoading: false,
        error: e.toString(),
      );
    }

    return false;
  }

  /// Sign in user with email and password
  Future<bool> signInUser({
    required String email,
    required String password,
  }) async {
    state = state.copyWith(isLoggingIn: true, isLoading: true, error: null);

    try {
      if (kDebugMode) {
        print('DEBUG: 🔐 Starting login for: $email');
      }

      final user = await EmailAuthService.signInWithEmailPassword(
        email: email,
        password: password,
      );

      if (user != null) {
        // Load user data from Firestore
        final userData = await EmailAuthService.getUserData(user.uid);
        if (userData != null) {
          final emailUser = EmailUser.fromFirebaseUser(user, userData);
          state = state.copyWith(
            user: emailUser,
            isAuthenticated: true,
            isLoggingIn: false,
            isLoading: false,
            error: null,
          );

          if (kDebugMode) {
            print('DEBUG: ✅ Login successful: ${user.uid}');
          }
          return true;
        }
      }
    } catch (e) {
      if (kDebugMode) {
        print('DEBUG: ❌ Login failed: $e');
      }
      state = state.copyWith(
        isLoggingIn: false,
        isLoading: false,
        error: e.toString(),
      );
    }

    return false;
  }

  /// Send password reset email
  Future<bool> sendPasswordReset(String email) async {
    try {
      await EmailAuthService.sendPasswordResetEmail(email);
      return true;
    } catch (e) {
      state = state.copyWith(error: e.toString());
      return false;
    }
  }

  /// Sign out user
  Future<void> signOut() async {
    try {
      await EmailAuthService.signOut();
      state = const EmailAuthState();
      if (kDebugMode) {
        print('DEBUG: ✅ User signed out successfully');
      }
    } catch (e) {
      if (kDebugMode) {
        print('DEBUG: ❌ Sign out failed: $e');
      }
      state = state.copyWith(error: e.toString());
    }
  }

  /// Clear error state
  void clearError() {
    state = state.copyWith(error: null);
  }

  /// Check if user is authenticated
  bool get isAuthenticated => state.isAuthenticated && state.user != null;

  /// Get current user
  EmailUser? get currentUser => state.user;
}

/// Email Auth Provider
final emailAuthProvider = StateNotifierProvider<EmailAuthNotifier, EmailAuthState>((ref) {
  return EmailAuthNotifier();
});
