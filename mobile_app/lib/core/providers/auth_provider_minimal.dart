import 'package:flutter/foundation.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:firebase_auth/firebase_auth.dart';

/// Simple user model for authentication (backward compatible)
class AppUser {
  final String uid;
  final String? email;
  final String? displayName;
  final String? phoneNumber;
  final String? photoURL;
  final bool isEmailVerified;

  const AppUser({
    required this.uid,
    this.email,
    this.displayName,
    this.phoneNumber,
    this.photoURL,
    required this.isEmailVerified,
  });

  AppUser copyWith({
    String? uid,
    String? email,
    String? displayName,
    String? phoneNumber,
    String? photoURL,
    bool? isEmailVerified,
  }) {
    return AppUser(
      uid: uid ?? this.uid,
      email: email ?? this.email,
      displayName: displayName ?? this.displayName,
      phoneNumber: phoneNumber ?? this.phoneNumber,
      photoURL: photoURL ?? this.photoURL,
      isEmailVerified: isEmailVerified ?? this.isEmailVerified,
    );
  }
}

/// Simple authentication state
class AuthState {
  final AppUser? user;
  final bool isLoading;
  final String? error;
  final bool isAuthenticated;

  const AuthState({
    this.user,
    this.isLoading = false,
    this.error,
    this.isAuthenticated = false,
  });

  AuthState copyWith({
    AppUser? user,
    bool? isLoading,
    String? error,
    bool? isAuthenticated,
  }) {
    return AuthState(
      user: user ?? this.user,
      isLoading: isLoading ?? this.isLoading,
      error: error,
      isAuthenticated: isAuthenticated ?? this.isAuthenticated,
    );
  }
}

/// Simple Authentication Provider (Clean - No Phone Auth Dependencies)
class AuthNotifier extends StateNotifier<AuthState> {
  AuthNotifier() : super(const AuthState()) {
    _checkAuthState();
  }

  final FirebaseAuth _auth = FirebaseAuth.instance;

  /// Check current authentication state
  void _checkAuthState() {
    final user = _auth.currentUser;
    if (user != null) {
      final appUser = AppUser(
        uid: user.uid,
        email: user.email,
        displayName: user.displayName,
        phoneNumber: user.phoneNumber,
        photoURL: user.photoURL,
        isEmailVerified: user.emailVerified,
      );
      state = state.copyWith(
        user: appUser,
        isAuthenticated: true,
      );
    }
  }

  /// Sign out user
  Future<void> signOut() async {
    try {
      await _auth.signOut();
      state = const AuthState();
      if (kDebugMode) {
        print('DEBUG: ✅ User signed out successfully');
      }
    } catch (e) {
      if (kDebugMode) {
        print('DEBUG: ❌ Sign out failed: $e');
      }
      state = state.copyWith(error: e.toString());
    }
  }

  /// DEPRECATED: Phone authentication methods (kept for backward compatibility)
  Future<bool> sendOTP(String phoneNumber) async {
    state = state.copyWith(
      error:
          'Phone authentication is no longer supported. Please use email authentication.',
    );
    return false;
  }

  Future<bool> verifyOTP(String otp) async {
    state = state.copyWith(
      error:
          'Phone authentication is no longer supported. Please use email authentication.',
    );
    return false;
  }

  Future<bool> registerWithPhone({
    required String phoneNumber,
    required String name,
    required String email,
    required String officeName,
    required String designation,
  }) async {
    state = state.copyWith(
      error:
          'Phone authentication is no longer supported. Please use email authentication.',
    );
    return false;
  }

  Future<bool> verifyRegistrationOTP(String otp) async {
    state = state.copyWith(
      error:
          'Phone authentication is no longer supported. Please use email authentication.',
    );
    return false;
  }

  Future<bool> resendRegistrationOTP() async {
    state = state.copyWith(
      error:
          'Phone authentication is no longer supported. Please use email authentication.',
    );
    return false;
  }

  Future<void> completeOnboarding() async {
    // This method can be implemented if needed
    if (kDebugMode) {
      print('DEBUG: ✅ Onboarding completed');
    }
  }

  /// Get current user
  AppUser? get currentUser => state.user;

  /// Check if user is authenticated
  bool get isAuthenticated => state.isAuthenticated && state.user != null;

  /// Clear error state
  void clearError() {
    state = state.copyWith(error: null);
  }

  /// Refresh user data
  Future<void> refreshUser() async {
    try {
      await _auth.currentUser?.reload();
      _checkAuthState();
    } catch (e) {
      if (kDebugMode) {
        print('DEBUG: ❌ Failed to refresh user: $e');
      }
    }
  }

  /// Listen to auth state changes
  void listenToAuthChanges() {
    _auth.authStateChanges().listen((User? user) {
      if (user != null) {
        final appUser = AppUser(
          uid: user.uid,
          email: user.email,
          displayName: user.displayName,
          phoneNumber: user.phoneNumber,
          photoURL: user.photoURL,
          isEmailVerified: user.emailVerified,
        );
        state = state.copyWith(
          user: appUser,
          isAuthenticated: true,
          error: null,
        );
      } else {
        state = const AuthState();
      }
    });
  }
}

/// Auth Provider (Clean)
final authProvider = StateNotifierProvider<AuthNotifier, AuthState>((ref) {
  final notifier = AuthNotifier();
  notifier.listenToAuthChanges();
  return notifier;
});

/// Current User Provider (for backward compatibility)
final currentUserProvider = Provider<AppUser?>((ref) {
  final authState = ref.watch(authProvider);
  return authState.user;
});
